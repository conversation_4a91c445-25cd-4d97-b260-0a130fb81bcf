# Thingino Provisioning System

## Overview

The Thingino Provisioning System is an automated configuration mechanism that allows devices to fetch and apply configuration settings from a remote server during boot. This system enables zero-touch deployment and centralized management of Thingino devices.

## How It Works

### 1. Boot Process Integration

The provisioning system runs as an init script (`S98provision`) during the boot process. It executes after network initialization but before most services start, ensuring network connectivity is available.

### 2. Configuration Discovery

The system uses two methods to discover the provisioning server:

#### Method 1: DHCP Option 160 (Recommended)
- The device requests DHCP option 160 during network initialization
- The DHCP server responds with the provisioning server URL
- This method enables automatic discovery without pre-configuration

#### Method 2: Manual Configuration
- Set the `provisioning_server` variable in the device configuration
- Useful for static network configurations or testing

### 3. Device Identification

The system identifies devices using their MAC address with the following priority:
1. **usb0** (USB network interface) - highest priority
2. **eth0** (Ethernet interface) - medium priority  
3. **wlan0** (WiFi interface) - lowest priority

The MAC address is used to construct the configuration file name on the server.

### 4. Configuration Retrieval

The system attempts to download a device-specific configuration file:
1. First tries: `http://server/thingino-{mac_lowercase}.conf`
2. If not found, tries: `http://server/thingino-{mac_uppercase}.conf`
3. If neither exists, provisioning is skipped (not an error)

### 5. Configuration Processing

Downloaded configuration files are validated and processed in three sections:
- **UENV**: U-Boot environment variables
- **SYSTEM**: Thingino system configuration
- **USER**: Custom shell commands

## DHCP Option 160 Setup

### DHCP Server Configuration

#### ISC DHCP Server (dhcpd)
```
option provisioning-server code 160 = text;

subnet *********** netmask ************* {
    range ************0 *************;
    option routers ***********;
    option domain-name-servers *******;
    option provisioning-server "http://************:8080";
}
```

#### Dnsmasq
```
dhcp-option=160,"http://************:8080"
```

#### OpenWrt/LEDE
```
uci set dhcp.lan.dhcp_option="160,http://************:8080"
uci commit dhcp
/etc/init.d/dnsmasq restart
```

### Provisioning Server Setup

The provisioning server can be any HTTP server (Apache, Nginx, Python SimpleHTTPServer, etc.) serving configuration files.

#### Simple Python Server Example
```bash
#!/bin/bash
# Start a simple provisioning server
cd /path/to/config/files
python3 -m http.server 8080
```

## Configuration File Format

Configuration files must:
1. Start with the marker `!THINGINO-CONFIG` on the first line
2. Use INI-style sections: `[SECTION_NAME]`
3. Be named `thingino-{MAC_ADDRESS}.conf`

### Sample Configuration File

```ini
!THINGINO-CONFIG

[UENV]
# U-Boot environment variables
# These are applied using fw_setenv
timezone=America/New_York
ntp_server=pool.ntp.org
wifi_ssid=MyNetwork
wifi_pass=MyPassword

[SYSTEM]
# Thingino system configuration
# These are applied using the 'conf' command
motion_detection=true
rtsp_enabled=true
rtsp_port=554
web_port=80
admin_password=newpassword

[USER]
# Custom shell commands
# These are executed as shell scripts
echo "Device provisioned at $(date)" >> /tmp/provision.log
# Configure custom LED pattern
echo 1 > /sys/class/leds/status/brightness
# Install custom packages (if available)
# opkg update && opkg install custom-package
```

## Configuration Sections

### UENV Section
Sets U-Boot environment variables using `fw_setenv`. Common variables include:
- `timezone`: System timezone
- `ntp_server`: NTP server for time synchronization
- `wifi_ssid`: WiFi network name
- `wifi_pass`: WiFi password
- `hostname`: Device hostname

### SYSTEM Section  
Configures Thingino system settings using the `conf` command. Examples:
- `motion_detection`: Enable/disable motion detection
- `rtsp_enabled`: Enable/disable RTSP streaming
- `web_port`: Web interface port
- `admin_password`: Admin password

### USER Section
Executes custom shell commands for advanced configuration:
- Custom service configuration
- Package installation
- File modifications
- Custom scripts

## File Naming Convention

Configuration files must follow this naming pattern:
- `thingino-{MAC_ADDRESS}.conf`
- MAC address without colons (e.g., `001122334455`)
- Both lowercase and uppercase MAC addresses are supported

### Examples
- Device with MAC `00:11:22:33:44:55`:
  - `thingino-001122334455.conf` (lowercase)
  - `thingino-001122334455.conf` (uppercase)

## Provisioning Status

### Completion Tracking
- After successful provisioning, the system sets `provisioning_complete=true`
- Subsequent boots skip provisioning if this flag is set
- To re-provision, remove this flag: `fw_setenv provisioning_complete`

### Logging
The provisioning system logs all activities:
- Console output with colored messages
- Syslog entries tagged with `thingino-provision`
- Detailed execution logs for troubleshooting

## Troubleshooting

### Common Issues

1. **No provisioning server configured**
   - Ensure DHCP option 160 is set, or manually configure `provisioning_server`

2. **Configuration file not found**
   - Verify MAC address format in filename
   - Check server accessibility
   - Ensure HTTP server is running

3. **Invalid configuration file**
   - Verify `!THINGINO-CONFIG` marker is present
   - Check file syntax and encoding

4. **Commands not found**
   - `fw_setenv` missing: U-Boot tools not installed
   - `conf` missing: Thingino configuration tools not available

### Debug Mode
Enable verbose logging by setting debug mode in the device configuration.

## Security Considerations

1. **Network Security**: Use HTTPS for sensitive configurations
2. **Access Control**: Restrict provisioning server access
3. **Configuration Validation**: Validate configuration content
4. **Credential Management**: Avoid plain-text passwords when possible

## Best Practices

1. **Version Control**: Keep configuration files in version control
2. **Testing**: Test configurations on development devices first
3. **Backup**: Backup working configurations before changes
4. **Documentation**: Document custom USER section commands
5. **Monitoring**: Monitor provisioning success/failure rates
