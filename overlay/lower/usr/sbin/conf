#!/bin/sh

. /usr/share/common

if [ ! -f "$CONFIG_FILE" ]; then
	echo_error "Config file $CONFIG_FILE not found"
	exit 1
fi

show_help() {
	local app=$(basename $0)
	echo "Thingino System Config Tool
Usage:
 $app g <param>           - Get parameter value
 $app s <param> <value>   - Set parameter value
 $app d <param>           - Delete parameter
 $app l [<filter>]        - List parameters matching filter
 $app --script <file>     - Import parameters from file (var=val format)
" >&2
	exit 1
}

backup() {
	cp "$CONFIG_FILE" "/tmp/$(basename $CONFIG_FILE).$(date +%s).bak"
}

case "$1" in
	g*)
		[ -z "$2" ] && show_help
		awk -F= "/^$2=/{gsub(/\"/,\"\",\$2);print \$2}" "$CONFIG_FILE"
		;;
	s*)
		[ -z "$3" ] && show_help
		backup
		value="$(echo "$3" | sed 's/"/\"/g')"
		if grep -q "^$2=" "$CONFIG_FILE"; then
			sed -i "/^$2=/c$2=\"$value\"" "$CONFIG_FILE"
		else
			tmpfile=$(mktemp)
			cat "$CONFIG_FILE" > "$tmpfile"
			echo "$2=\"$value\"" >> "$tmpfile"
			sed -i "/^\s*$/d" "$tmpfile"
			sort -u < "$tmpfile" > "$CONFIG_FILE"
		fi
		;;
	d*)
		[ -z "$2" ] && show_help
		backup
		sed -i "/^$2=/d" "$CONFIG_FILE"
		;;
	l*)
		grep "$2" "$CONFIG_FILE"
		;;
	--script)
		[ -z "$2" ] && show_help
		if [ ! -f "$2" ]; then
			echo_error "Script file $2 not found"
			exit 1
		fi
		backup
		# Read the script file and process each var=val line
		while IFS= read -r line; do
			# Skip empty lines and comments
			line=$(echo "$line" | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')
			[ -z "$line" ] && continue
			[ "${line:0:1}" = "#" ] && continue
			
			# Extract parameter and value
			if echo "$line" | grep -q "^[a-zA-Z_][a-zA-Z0-9_]*="; then
				param=$(echo "$line" | cut -d= -f1)
				value=$(echo "$line" | cut -d= -f2-)
				# Remove quotes if present
				value=$(echo "$value" | sed 's/^"//;s/"$//')
				# Escape quotes in value
				value=$(echo "$value" | sed 's/"/\\"/g')
				
				# Update or add parameter
				if grep -q "^$param=" "$CONFIG_FILE"; then
					sed -i "/^$param=/c$param=\"$value\"" "$CONFIG_FILE"
				else
					tmpfile=$(mktemp)
					cat "$CONFIG_FILE" > "$tmpfile"
					echo "$param=\"$value\"" >> "$tmpfile"
					sed -i "/^\s*$/d" "$tmpfile"
					sort -u < "$tmpfile" > "$CONFIG_FILE"
				fi
			fi
		done < "$2"
		;;
	*)
		show_help
		;;
esac

exit 0
