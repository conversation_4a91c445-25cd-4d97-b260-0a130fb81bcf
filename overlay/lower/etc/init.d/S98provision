#!/bin/sh
#
# Thingino Provisioning System Init Script
# This script checks for device-specific configuration from a provisioning server
# and applies the configuration to the system
#

. /usr/share/common

# Configuration
PROVISIONING_SERVER="${provisioning_server:-}"
TEMP_DIR="/tmp/provisioning"
CONFIG_MARKER="!THINGINO-CONFIG"
SCRIPT_NAME="$(basename $0)"

# Logging function
log() {
	logger -t "thingino-provision" "$@"
	echo "[$SCRIPT_NAME] $@" >&2
}

# Error handling
die() {
	log "ERROR: $@"
	exit 1
}

# Get MAC address based on priority: usb0 > eth0 > wlan0
get_mac_address() {
	local mac=""
	local interface=""
	
	# Check interfaces in priority order
	if [ -d "/sys/class/net/usb0" ]; then
		interface="usb0"
	elif [ -d "/sys/class/net/eth0" ]; then
		interface="eth0"
	elif [ -d "/sys/class/net/wlan0" ]; then
		interface="wlan0"
	else
		die "No network interface found (usb0, eth0, or wlan0)"
	fi
	
	# Read MAC address (without colons, keeping original case)
	if [ -r "/sys/class/net/${interface}/address" ]; then
		mac=$(cat "/sys/class/net/${interface}/address" | tr -d ':')
		log "Using MAC address from ${interface}: ${mac}"
		echo "${mac}"
	else
		die "Cannot read MAC address from ${interface}"
	fi
}

# Parse a section from the config file
parse_section() {
	local config_file="$1"
	local section="$2"
	local in_section=0
	
	while IFS= read -r line; do
		# Skip empty lines and comments
		case "$line" in
			""|\#*) continue ;;
		esac
		
		# Check if we're entering the desired section
		if echo "$line" | grep -q "^\[${section}\]$"; then
			in_section=1
			continue
		fi
		
		# Check if we're entering a different section
		if echo "$line" | grep -q "^\[.*\]$"; then
			in_section=0
			continue
		fi
		
		# If we're in the desired section, output the line
		if [ $in_section -eq 1 ]; then
			echo "$line"
		fi
	done < "$config_file"
}

# Process UENV section
process_uenv() {
	local config_file="$1"
	local temp_file="${TEMP_DIR}/uenv_script.txt"
	
	log "Processing UENV section"
	
	# Extract UENV entries
	parse_section "$config_file" "UENV" > "$temp_file"
	
	if [ -s "$temp_file" ]; then
		if command -v fw_setenv >/dev/null 2>&1; then
			log "Applying UENV settings"
			if fw_setenv --script "$temp_file" 2>&1 | logger -t "thingino-provision"; then
				log "UENV settings applied successfully"
			else
				log "ERROR: Failed to apply UENV settings"
			fi
		else
			log "WARNING: fw_setenv not found, skipping UENV section"
		fi
	else
		log "No UENV entries found"
	fi
	
	rm -f "$temp_file"
}

# Process SYSTEM section
process_system() {
	local config_file="$1"
	local temp_file="${TEMP_DIR}/system_script.txt"
	
	log "Processing SYSTEM section"
	
	# Extract SYSTEM entries
	parse_section "$config_file" "SYSTEM" > "$temp_file"
	
	if [ -s "$temp_file" ]; then
		if command -v conf >/dev/null 2>&1; then
			log "Applying SYSTEM settings"
			if conf --script "$temp_file" 2>&1 | logger -t "thingino-provision"; then
				log "SYSTEM settings applied successfully"
			else
				log "ERROR: Failed to apply SYSTEM settings"
			fi
		else
			log "WARNING: conf command not found, skipping SYSTEM section"
		fi
	else
		log "No SYSTEM entries found"
	fi
	
	rm -f "$temp_file"
}

# Process USER section (shell commands)
process_user() {
	local config_file="$1"
	local temp_file="${TEMP_DIR}/user_commands.sh"
	
	log "Processing USER section"
	
	# Extract USER commands
	echo "#!/bin/sh" > "$temp_file"
	echo "# Auto-generated user commands from provisioning" >> "$temp_file"
	parse_section "$config_file" "USER" >> "$temp_file"
	
	if [ $(wc -l < "$temp_file") -gt 2 ]; then
		chmod +x "$temp_file"
		log "Executing USER commands"
		if sh -x "$temp_file" 2>&1 | logger -t "thingino-provision"; then
			log "USER commands executed successfully"
		else
			log "ERROR: Some USER commands may have failed"
		fi
	else
		log "No USER commands found"
	fi
	
	rm -f "$temp_file"
}

# Main provisioning function
do_provisioning() {
	local mac_address
	local config_url
	local config_file="${TEMP_DIR}/provision.conf"
	
	# Check if provisioning has already been completed
	if command -v conf >/dev/null 2>&1; then
		if [ "$(conf g provisioning_complete 2>/dev/null)" = "true" ]; then
			log "Provisioning already completed, skipping"
			return 0
		fi
	fi
	
	# Check if provisioning server is configured
	if [ -z "$PROVISIONING_SERVER" ]; then
		log "No provisioning server configured, skipping provisioning"
		return 0
	fi
	
	# Create temp directory
	mkdir -p "$TEMP_DIR" || die "Failed to create temp directory"
	
	# Get MAC address
	mac_address=$(get_mac_address) || exit 1
	
	# Build configuration URLs (try both lowercase and uppercase)
	mac_lower=$(echo "$mac_address" | tr 'A-Z' 'a-z')
	mac_upper=$(echo "$mac_address" | tr 'a-z' 'A-Z')
	
	# Try lowercase first
	config_url="${PROVISIONING_SERVER}/thingino-${mac_lower}.conf"
	log "Trying provisioning config: ${config_url}"
	
	# Download configuration file
	if wget -q -O "$config_file" "$config_url" 2>/dev/null; then
		log "Provisioning config downloaded successfully (lowercase MAC)"
	else
		# Try uppercase
		config_url="${PROVISIONING_SERVER}/thingino-${mac_upper}.conf"
		log "Trying provisioning config: ${config_url}"
		
		if wget -q -O "$config_file" "$config_url" 2>/dev/null; then
			log "Provisioning config downloaded successfully (uppercase MAC)"
		else
			log "No provisioning config found for MAC ${mac_address}"
			rm -rf "$TEMP_DIR"
			return 0
		fi
	fi
	
	# Verify config file has the required marker
	if ! grep -q "^${CONFIG_MARKER}$" "$config_file"; then
		log "ERROR: Invalid config file - missing ${CONFIG_MARKER} marker"
		rm -rf "$TEMP_DIR"
		return 1
	fi
	
	log "Valid provisioning config found, processing sections"
	
	# Process each section
	process_uenv "$config_file"
	process_system "$config_file"
	process_user "$config_file"
	
	# Mark provisioning as complete
	if command -v conf >/dev/null 2>&1; then
		log "Setting provisioning_complete flag"
		conf s provisioning_complete true
	fi
	
	# Cleanup
	rm -rf "$TEMP_DIR"
	log "Provisioning completed"
	
	return 0
}

# Script entry point
case "$1" in
	start)
		log "Starting Thingino provisioning"
		do_provisioning
		;;
	stop)
		# Nothing to stop
		;;
	restart|reload)
		$0 start
		;;
	*)
		echo "Usage: $0 {start|stop|restart|reload}"
		exit 1
		;;
esac

exit 0