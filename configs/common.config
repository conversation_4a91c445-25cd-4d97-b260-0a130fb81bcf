admin_name="Thingino Admin"
day_night_color="true"
day_night_ir850="true"
day_night_ir940="true"
day_night_ircut="true"
day_night_max="15000"
day_night_min="500"
day_night_white="true"
debug_enabled="false"
development_enabled="false"
enable_updates="true"
portal_device="wlan0"
provisioning_enabled="false"
rsyslog_local="false"
rsyslog_port="514"
streamer_disabled="false"
telegrambot_command_0="start"
telegrambot_command_1="help"
telegrambot_command_2="info"
telegrambot_command_3="diag"
telegrambot_command_4="snap"
telegrambot_command_5="clip"
telegrambot_description_0="Start conversation"
telegrambot_description_1="Request help"
telegrambot_description_2="Information about system"
telegrambot_description_3="Gather diagnostic information"
telegrambot_description_4="Take a snapshot"
telegrambot_description_5="Record a 5 second clip"
telegrambot_script_0="echo \"Hello\""
telegrambot_script_1="echo \"Try https://thingino.com/\""
telegrambot_script_2="cat /etc/os-release"
telegrambot_script_3="thingino-diag"
telegrambot_script_4="send2telegram -c \$chat_id snap"
telegrambot_script_5="send2telegram -c \$chat_id clip"
usb_console="false"
watchdog_device="/dev/watchdog"
watchdog_disable="false"
watchdog_timeout="60"
webui_level="user"
webui_theme="dark"
wireguard_enabled="false"
wlanap_enabled="false"
